#pragma once

#include <cstdint>
#include <iox/string.hpp>
#include <string>

namespace mower_msgs::msg
{

enum class SocExceptionLevel : int16_t
{
    WARNING = 0,
    ERROR = 1,
    NONE = 2,
    UNKNOWN,
};

enum class SocExceptionValue : uint16_t
{
    NO_EXCEPTION = 0x0000,
    // camera stream exception
    SW_CAMERA_STREAM_EXCEPTION = 0x1000,
    // high CPU temperature exception
    SW_HIGH_CPU_TEMP_EXCEPTION = 0x1001,
    // serial port communication exception
    SW_SERIAL_PORT_COMMUICATION_EXCEPTION = 0x1002,
    // CPU frequency exception
    SW_CPU_FREQUENCY_EXCEPTION = 0x1003,
    // BPU frequency exception
    SW_BPU_FREQUENCY_EXCEPTION = 0x1004,
    // high CPU frequency exception
    SW_HIGH_CPU_USAGE_EXCEPTION = 0x1005,
    // high memory usage exception
    SW_HIGH_MEMORY_USAGE_EXCEPTION = 0x1006,
    // high disk occupancy exception
    SW_HIGH_DISK_USAGE_EXCEPTION = 0x1007,
    // self check fail exception
    SW_SELF_CHECK_FAIL_EXCEPTION = 0x1008,
    // node offline exception
    SW_NODE_OFFLINE_EXCEPTION = 0x1009,
    // video brightness low exception
    VIDEO_BRIGHTNESS_LOW_EXCEPTION = 0x100A,
    // segmentation exception
    ALG_SEGMENTATION_INIT_EXCEPTION = 0x2000,
    ALG_SEGMENTATION_PARAM_ERROR_EXCEPTION = 0x2001,
    ALG_SEGMENTATION_EXECUTE_ERROR_EXCEPTION = 0x2002,
    ALG_SEGMENTATION_GET_BEV_PARAM_ERROR_EXCEPTION = 0x2003,
    // obstacle detect exception
    ALG_COMMON_OBJECT_DETECTION_INIT_EXCEPTION = 0x3000,
    ALG_COMMON_OBJECT_DETECTION_PARAM_ERROR_EXCEPTION = 0x3001,
    ALG_COMMON_OBJECT_DETECTION_EXECUTE_ERROR_EXCEPTION = 0x3002,
    ALG_COMMON_OBJECT_DETECTION_SAFTY_COUNTOR_EXCEPTION = 0x3003,
    // perception fusion exception
    ALG_PERCEPTION_FUSION_INIT_EXCEPTION = 0x4000,
    ALG_PERCEPTION_FUSION_PARAM_ERROR_EXCEPTION = 0x4001,
    ALG_PERCEPTION_FUSION_EXECUTE_ERROR_EXCEPTION = 0x4002,
    ALG_PERCEPTION_FUSION_GET_INTRINSIC_PARAM_ERROR_EXCEPTION = 0x4003,
    ALG_PERCEPTION_FUSION_SEG_INIT_EXCEPTION = 0x4004,
    // perception occulsion exception
    ALG_PERCEPTION_OCCLUSION_INIT_EXCEPTION = 0x4200,
    ALG_PERCEPTION_OCCLUSION_PARAM_ERROR_EXCEPTION = 0x4201,
    ALG_PERCEPTION_OCCLUSION_EXECUTE_ERROR_EXCEPTION = 0x4202,
    ALG_PERCEPTION_OCCLUSION_CAMERA_IS_OBSTRUCTED = 0x4203,
    // back charge mark detect exception
    ALG_BACK_CHARGE_MARK_DETECT_INIT_EXCEPTION = 0x6000,
    ALG_BACK_CHARGE_MARK_DETECT_PARAM_ERROR_EXCEPTION = 0x6001,
    ALG_BACK_CHARGE_MARK_DETECT_EXECUTE_ERROR_EXCEPTION = 0x6002,
    // back charge localization exception
    ALG_CHARGE_STATION_LOCALIZATION_INIT_EXCEPTION = 0x6200,
    ALG_CHARGE_STATION_LOCALIZATION_PARAM_ERROR_EXCEPTION = 0x6201,
    ALG_CHARGE_STATION_LOCALIZATION_EXECUTE_ERROR_EXCEPTION = 0x6202,
    ALG_CHARGE_STATION_LOCALIZATION_GET_INTRINSIC_PARAM_ERROR_EXCEPTION = 0x6203,
    // localization motion detection exception
    ALG_LOCALIZATION_MOTION_DETECTION_INIT_EXCEPTION = 0x6300,
    ALG_LOCALIZATION_MOTION_DETECTION_PARAM_ERROR_EXCEPTION = 0x6301,
    ALG_LOCALIZATION_MOTION_DETECTION_EXECUTE_ERROR_EXCEPTION = 0x6302,
    // cross region beacon detect exception
    ALG_CROSS_REGION_DETECTION_INIT_EXCEPTION = 0x7000,
    ALG_CROSS_REGION_DETECTION_PARAM_ERROR_EXCEPTION = 0x7001,
    ALG_CROSS_REGION_DETECTION_EXECUTE_ERROR_EXCEPTION = 0x7002,
    // cross region localization exception
    ALG_CROSS_REGION_LOCALIZATION_INIT_EXCEPTION = 0x8000,
    ALG_CROSS_REGION_LOCALIZATION_PARAM_ERROR_EXCEPTION = 0x8001,
    ALG_CROSS_REGION_LOCALIZATION_EXECUTE_ERROR_EXCEPTION = 0x8002,
    ALG_CROSS_REGION_LOCALIZATION_GET_INTRINSIC_PARAM_ERROR_EXCEPTION = 0x8003,
    // area estimation exception
    ALG_LOCALIZATION_EREA_ESTIMATION_INIT_EXCEPTION = 0x8200,
    ALG_LOCALIZATION_EREA_ESTIMATION_PARAM_ERROR_EXCEPTION = 0x8201,
    ALG_LOCALIZATION_EREA_ESTIMATION_EXECUTE_ERROR_EXCEPTION = 0x8202,
    // weak lightness exception
    ALG_PNC_WEAK_LIGHTNESS_EXCEPTION = 0x9000,
    // not grass exception
    ALG_PNC_NOT_GRASS_EXCEPTION = 0x9001,
    // undocking failure exception
    ALG_PNC_UNDOCKING_FAILED_EXCEPTION = 0x9002,
    // recharge failure exception (failed 3 times)
    ALG_PNC_RECHARGE_FAIL_3_TIMES_EXCEPTION = 0x9003,
    // recharge timeout exception
    ALG_PNC_RECHARGE_TIMEOUT_EXCEPTION = 0x9004,
    // QR code timeout exception during recharge
    ALG_PNC_QR_CODE_TIMEOUT_EXCEPTION = 0x9005,
    // multiple recovery measures triggered in a short time
    ALG_PNC_MULTIPLE_RECOVERY_MEASURES_EXCEPTION = 0x9006,
    // exploration recharge timeout exception
    ALG_PNC_EXPLORATION_RECHARGE_TIMEOUT_EXCEPTION = 0x9007,
    // exploration beacon timeout exception
    ALG_PNC_EXPLORATION_BEACON_TIMEOUT_EXCEPTION = 0x9008,
    // cross-region beacon timeout exception
    ALG_PNC_CROSS_REGION_BEACON_TIMEOUT_EXCEPTION = 0x9009,
    // cross-region grass timeout exception
    ALG_PNC_CROSS_REGION_GRASS_TIMEOUT_EXCEPTION = 0x900A,
    // random mowing process timeout without detecting grass exception
    ALG_PNC_RANDOM_MOWING_GRASS_TIMEOUT_EXCEPTION = 0x900B,
    // spiral mowing process timeout without detecting grass exception
    ALG_PNC_SPIRAL_MOWING_GRASS_TIMEOUT_EXCEPTION = 0x900C,
    // edge follow multiple consecutive obstacle avoidance failures exception
    ALG_PNC_EDGE_FOLLOW_OBSTACLE_AVOIDANCE_FAILURES_EXCEPTION = 0x900D,
    // Self-checking failure exception
    ALG_PNC_SELF_CHECK_FAILED_EXCEPTION = 0x900E,

    // During mowing, non-grassland anomaly within 5 to 30 seconds - Warning
    ALG_PNC_MOWING_NON_GRASSLAND_5_TO_30S_EXCEPTION = 0x900F,
    // During mowing, non-grassland anomaly over 30 seconds - Error
    ALG_PNC_MOWING_NON_GRASSLAND_OVER_30S_EXCEPTION = 0x9010,
    // During mowing, slipping anomaly - Warning
    ALG_PNC_MOWING_SLIPPING_EXCEPTION = 0x9011,
    // Random mowing trap -Error
    ALG_PNC_RANDOM_MOWING_TRAP_EXCEPTION = 0x9012,
    // No Recharge Station -Error
    ALG_PNC_NO_RECHARGE_STATION_EXCEPTION = 0x9013,
    // Cross-region beacon pairing error - Error
    ALG_PNC_CROSS_ZONE_BEACON_PAIRING_EXCEPTION = 0x9014,
    // SW Command execute Error
    ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION = 0x9015,
    // Cross-region cannot find non lawn - Error
    ALG_PNC_CROSS_ZONE_NON_LAWN_NOT_FOUND_EXCEPTION = 0x9016,
    // Enable strong recovery mode (Warning level)
    ALG_PNC_STRONG_RECOVERY_MODE_ENABLED_WARNING = 0x9017,
    // Strong recovery mode failed (Error level)
    ALG_PNC_STRONG_RECOVERY_MODE_FAILED_ERROR = 0x9018,
    UNKNOWN,
};

struct SocException
{
    uint64_t timestamp{0};
    iox::string<128> node_name;
    SocExceptionLevel exception_level{SocExceptionLevel::NONE};
    SocExceptionValue exception_value{SocExceptionValue::NO_EXCEPTION};
};

inline bool operator==(const SocException &lhs, const SocException &rhs)
{
    return lhs.exception_level == rhs.exception_level && lhs.exception_value == rhs.exception_value;
}
inline bool operator!=(const SocException &lhs, const SocException &rhs) { return !(lhs == rhs); }

inline std::string asStringLiteral(SocExceptionLevel exception_level)
{
    switch (exception_level)
    {
    case SocExceptionLevel::WARNING:
        return "WARNING";
    case SocExceptionLevel::ERROR:
        return "ERROR";
    case SocExceptionLevel::NONE:
        return "NONE";
    default:
        return "UNKNOWN";
    }
}

inline SocExceptionLevel fromStringToSocExceptionLevel(const std::string &exception_str)
{
    if (exception_str == "WARNING")
    {
        return SocExceptionLevel::WARNING;
    }
    else if (exception_str == "ERROR")
    {
        return SocExceptionLevel::ERROR;
    }
    else if (exception_str == "NONE")
    {
        return SocExceptionLevel::NONE;
    }
    else
    {
        return SocExceptionLevel::UNKNOWN;
    }
}

inline std::string asStringLiteral(SocExceptionValue exception_value)
{
    switch (exception_value)
    {
    case SocExceptionValue::NO_EXCEPTION:
        return "NO_EXCEPTION";
    case SocExceptionValue::SW_CAMERA_STREAM_EXCEPTION:
        return "SW_CAMERA_STREAM_EXCEPTION";
    case SocExceptionValue::SW_SERIAL_PORT_COMMUICATION_EXCEPTION:
        return "SW_SERIAL_PORT_COMMUICATION_EXCEPTION";
    case SocExceptionValue::SW_CPU_FREQUENCY_EXCEPTION:
        return "SW_CPU_FREQUENCY_EXCEPTION";
    case SocExceptionValue::SW_BPU_FREQUENCY_EXCEPTION:
        return "SW_BPU_FREQUENCY_EXCEPTION";
    case SocExceptionValue::SW_HIGH_CPU_USAGE_EXCEPTION:
        return "SW_HIGH_CPU_USAGE_EXCEPTION";
    case SocExceptionValue::SW_HIGH_MEMORY_USAGE_EXCEPTION:
        return "SW_HIGH_MEMORY_USAGE_EXCEPTION";
    case SocExceptionValue::SW_HIGH_DISK_USAGE_EXCEPTION:
        return "SW_HIGH_DISK_USAGE_EXCEPTION";
    case SocExceptionValue::SW_SELF_CHECK_FAIL_EXCEPTION:
        return "SW_SELF_CHECK_FAIL_EXCEPTION";
    case SocExceptionValue::SW_NODE_OFFLINE_EXCEPTION:
        return "SW_NODE_OFFLINE_EXCEPTION";
    case SocExceptionValue::VIDEO_BRIGHTNESS_LOW_EXCEPTION:
        return "VIDEO_BRIGHTNESS_LOW_EXCEPTION";
    case SocExceptionValue::SW_HIGH_CPU_TEMP_EXCEPTION:
        return "SW_HIGH_CPU_TEMP_EXCEPTION";
    case SocExceptionValue::ALG_SEGMENTATION_INIT_EXCEPTION:
        return "ALG_SEGMENTATION_INIT_EXCEPTION";
    case SocExceptionValue::ALG_SEGMENTATION_PARAM_ERROR_EXCEPTION:
        return "ALG_SEGMENTATION_PARAM_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_SEGMENTATION_EXECUTE_ERROR_EXCEPTION:
        return "ALG_SEGMENTATION_EXECUTE_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_SEGMENTATION_GET_BEV_PARAM_ERROR_EXCEPTION:
        return "ALG_SEGMENTATION_GET_BEV_PARAM_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_COMMON_OBJECT_DETECTION_INIT_EXCEPTION:
        return "ALG_COMMON_OBJECT_DETECTION_INIT_EXCEPTION";
    case SocExceptionValue::ALG_COMMON_OBJECT_DETECTION_PARAM_ERROR_EXCEPTION:
        return "ALG_COMMON_OBJECT_DETECTION_PARAM_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_COMMON_OBJECT_DETECTION_EXECUTE_ERROR_EXCEPTION:
        return "ALG_COMMON_OBJECT_DETECTION_EXECUTE_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_COMMON_OBJECT_DETECTION_SAFTY_COUNTOR_EXCEPTION:
        return "ALG_COMMON_OBJECT_DETECTION_SAFTY_COUNTOR_EXCEPTION";
    case SocExceptionValue::ALG_PERCEPTION_FUSION_INIT_EXCEPTION:
        return "ALG_PERCEPTION_FUSION_INIT_EXCEPTION";
    case SocExceptionValue::ALG_PERCEPTION_FUSION_PARAM_ERROR_EXCEPTION:
        return "ALG_PERCEPTION_FUSION_PARAM_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_PERCEPTION_FUSION_EXECUTE_ERROR_EXCEPTION:
        return "ALG_PERCEPTION_FUSION_EXECUTE_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_PERCEPTION_FUSION_GET_INTRINSIC_PARAM_ERROR_EXCEPTION:
        return "ALG_PERCEPTION_FUSION_GET_INTRINSIC_PARAM_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_PERCEPTION_FUSION_SEG_INIT_EXCEPTION:
        return "ALG_PERCEPTION_FUSION_SEG_INIT_EXCEPTION";
    case SocExceptionValue::ALG_PERCEPTION_OCCLUSION_INIT_EXCEPTION:
        return "ALG_PERCEPTION_OCCLUSION_INIT_EXCEPTION";
    case SocExceptionValue::ALG_PERCEPTION_OCCLUSION_PARAM_ERROR_EXCEPTION:
        return "ALG_PERCEPTION_OCCLUSION_PARAM_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_PERCEPTION_OCCLUSION_EXECUTE_ERROR_EXCEPTION:
        return "ALG_PERCEPTION_OCCLUSION_EXECUTE_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_PERCEPTION_OCCLUSION_CAMERA_IS_OBSTRUCTED:
        return "ALG_PERCEPTION_OCCLUSION_CAMERA_IS_OBSTRUCTED";
    case SocExceptionValue::ALG_BACK_CHARGE_MARK_DETECT_INIT_EXCEPTION:
        return "ALG_BACK_CHARGE_MARK_DETECT_INIT_EXCEPTION";
    case SocExceptionValue::ALG_BACK_CHARGE_MARK_DETECT_PARAM_ERROR_EXCEPTION:
        return "ALG_BACK_CHARGE_MARK_DETECT_PARAM_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_BACK_CHARGE_MARK_DETECT_EXECUTE_ERROR_EXCEPTION:
        return "ALG_BACK_CHARGE_MARK_DETECT_EXECUTE_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_CHARGE_STATION_LOCALIZATION_INIT_EXCEPTION:
        return "ALG_CHARGE_STATION_LOCALIZATION_INIT_EXCEPTION";
    case SocExceptionValue::ALG_CHARGE_STATION_LOCALIZATION_PARAM_ERROR_EXCEPTION:
        return "ALG_CHARGE_STATION_LOCALIZATION_PARAM_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_CHARGE_STATION_LOCALIZATION_EXECUTE_ERROR_EXCEPTION:
        return "ALG_CHARGE_STATION_LOCALIZATION_EXECUTE_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_CHARGE_STATION_LOCALIZATION_GET_INTRINSIC_PARAM_ERROR_EXCEPTION:
        return "ALG_CHARGE_STATION_LOCALIZATION_GET_INTRINSIC_PARAM_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_LOCALIZATION_MOTION_DETECTION_INIT_EXCEPTION:
        return "ALG_LOCALIZATION_MOTION_DETECTION_INIT_EXCEPTION";
    case SocExceptionValue::ALG_LOCALIZATION_MOTION_DETECTION_PARAM_ERROR_EXCEPTION:
        return "ALG_LOCALIZATION_MOTION_DETECTION_PARAM_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_LOCALIZATION_MOTION_DETECTION_EXECUTE_ERROR_EXCEPTION:
        return "ALG_LOCALIZATION_MOTION_DETECTION_EXECUTE_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_CROSS_REGION_DETECTION_INIT_EXCEPTION:
        return "ALG_CROSS_REGION_DETECTION_INIT_EXCEPTION";
    case SocExceptionValue::ALG_CROSS_REGION_DETECTION_PARAM_ERROR_EXCEPTION:
        return "ALG_CROSS_REGION_DETECTION_PARAM_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_CROSS_REGION_DETECTION_EXECUTE_ERROR_EXCEPTION:
        return "ALG_CROSS_REGION_DETECTION_EXECUTE_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_CROSS_REGION_LOCALIZATION_INIT_EXCEPTION:
        return "ALG_CROSS_REGION_LOCALIZATION_INIT_EXCEPTION";
    case SocExceptionValue::ALG_CROSS_REGION_LOCALIZATION_PARAM_ERROR_EXCEPTION:
        return "ALG_CROSS_REGION_LOCALIZATION_PARAM_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_CROSS_REGION_LOCALIZATION_EXECUTE_ERROR_EXCEPTION:
        return "ALG_CROSS_REGION_LOCALIZATION_EXECUTE_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_CROSS_REGION_LOCALIZATION_GET_INTRINSIC_PARAM_ERROR_EXCEPTION:
        return "ALG_CROSS_REGION_LOCALIZATION_GET_INTRINSIC_PARAM_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_LOCALIZATION_EREA_ESTIMATION_INIT_EXCEPTION:
        return "ALG_LOCALIZATION_EREA_ESTIMATION_INIT_EXCEPTION";
    case SocExceptionValue::ALG_LOCALIZATION_EREA_ESTIMATION_PARAM_ERROR_EXCEPTION:
        return "ALG_LOCALIZATION_EREA_ESTIMATION_PARAM_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_LOCALIZATION_EREA_ESTIMATION_EXECUTE_ERROR_EXCEPTION:
        return "ALG_LOCALIZATION_EREA_ESTIMATION_EXECUTE_ERROR_EXCEPTION";
    case SocExceptionValue::ALG_PNC_WEAK_LIGHTNESS_EXCEPTION:
        return "ALG_PNC_WEAK_LIGHTNESS_EXCEPTION";
    case SocExceptionValue::ALG_PNC_NOT_GRASS_EXCEPTION:
        return "ALG_PNC_NOT_GRASS_EXCEPTION";
    case SocExceptionValue::ALG_PNC_UNDOCKING_FAILED_EXCEPTION:
        return "ALG_PNC_UNDOCKING_FAILED_EXCEPTION";
    case SocExceptionValue::ALG_PNC_RECHARGE_FAIL_3_TIMES_EXCEPTION:
        return "ALG_PNC_RECHARGE_FAIL_3_TIMES_EXCEPTION";
    case SocExceptionValue::ALG_PNC_RECHARGE_TIMEOUT_EXCEPTION:
        return "ALG_PNC_RECHARGE_TIMEOUT_EXCEPTION";
    case SocExceptionValue::ALG_PNC_QR_CODE_TIMEOUT_EXCEPTION:
        return "ALG_PNC_QR_CODE_TIMEOUT_EXCEPTION";
    case SocExceptionValue::ALG_PNC_MULTIPLE_RECOVERY_MEASURES_EXCEPTION:
        return "ALG_PNC_MULTIPLE_RECOVERY_MEASURES_EXCEPTION";
    case SocExceptionValue::ALG_PNC_EXPLORATION_RECHARGE_TIMEOUT_EXCEPTION:
        return "ALG_PNC_EXPLORATION_RECHARGE_TIMEOUT_EXCEPTION";
    case SocExceptionValue::ALG_PNC_EXPLORATION_BEACON_TIMEOUT_EXCEPTION:
        return "ALG_PNC_EXPLORATION_BEACON_TIMEOUT_EXCEPTION";
    case SocExceptionValue::ALG_PNC_CROSS_REGION_BEACON_TIMEOUT_EXCEPTION:
        return "ALG_PNC_CROSS_REGION_BEACON_TIMEOUT_EXCEPTION";
    case SocExceptionValue::ALG_PNC_EDGE_FOLLOW_OBSTACLE_AVOIDANCE_FAILURES_EXCEPTION:
        return "ALG_PNC_EDGE_FOLLOW_OBSTACLE_AVOIDANCE_FAILURES_EXCEPTION";
    case SocExceptionValue::ALG_PNC_CROSS_REGION_GRASS_TIMEOUT_EXCEPTION:
        return "ALG_PNC_CROSS_REGION_GRASS_TIMEOUT_EXCEPTION";
    case SocExceptionValue::ALG_PNC_RANDOM_MOWING_GRASS_TIMEOUT_EXCEPTION:
        return "ALG_PNC_RANDOM_MOWING_GRASS_TIMEOUT_EXCEPTION";
    case SocExceptionValue::ALG_PNC_SPIRAL_MOWING_GRASS_TIMEOUT_EXCEPTION:
        return "ALG_PNC_SPIRAL_MOWING_GRASS_TIMEOUT_EXCEPTION";
    case SocExceptionValue::ALG_PNC_SELF_CHECK_FAILED_EXCEPTION:
        return "ALG_PNC_SELF_CHECK_FAILED_EXCEPTION";
    case SocExceptionValue::ALG_PNC_MOWING_NON_GRASSLAND_5_TO_30S_EXCEPTION:
        return "ALG_PNC_MOWING_NON_GRASSLAND_5_TO_30S_EXCEPTION";
    case SocExceptionValue::ALG_PNC_MOWING_NON_GRASSLAND_OVER_30S_EXCEPTION:
        return "ALG_PNC_MOWING_NON_GRASSLAND_OVER_30S_EXCEPTION";
    case SocExceptionValue::ALG_PNC_MOWING_SLIPPING_EXCEPTION:
        return "ALG_PNC_MOWING_SLIPPING_EXCEPTION";
    case SocExceptionValue::ALG_PNC_RANDOM_MOWING_TRAP_EXCEPTION:
        return "ALG_PNC_RANDOM_MOWING_TRAP_EXCEPTION";
    case SocExceptionValue::ALG_PNC_NO_RECHARGE_STATION_EXCEPTION:
        return "ALG_PNC_NO_RECHARGE_STATION_EXCEPTION";
    case SocExceptionValue::ALG_PNC_CROSS_ZONE_BEACON_PAIRING_EXCEPTION:
        return "ALG_PNC_CROSS_ZONE_BEACON_PAIRING_EXCEPTION";
    case SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION:
        return "ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION";
    case SocExceptionValue::ALG_PNC_CROSS_ZONE_NON_LAWN_NOT_FOUND_EXCEPTION:
        return "ALG_PNC_CROSS_ZONE_NON_LAWN_NOT_FOUND_EXCEPTION";
    case SocExceptionValue::ALG_PNC_STRONG_RECOVERY_MODE_ENABLED_WARNING:
        return "ALG_PNC_STRONG_RECOVERY_MODE_ENABLED_WARNING";
    case SocExceptionValue::ALG_PNC_STRONG_RECOVERY_MODE_FAILED_ERROR:
        return "ALG_PNC_STRONG_RECOVERY_MODE_FAILED_ERROR";
    default:
        return "UNKNOWN";
    }
}

inline SocExceptionValue fromStringToSocExceptionValue(const std::string &exception_str)
{
    for (int i = static_cast<int>(SocExceptionValue::NO_EXCEPTION);
         i <= static_cast<int>(SocExceptionValue::UNKNOWN); ++i)
    {
        SocExceptionValue value = static_cast<SocExceptionValue>(i);
        if (asStringLiteral(value) == exception_str)
        {
            return value;
        }
    }
    return SocExceptionValue::UNKNOWN;
}

} // namespace mower_msgs::msg

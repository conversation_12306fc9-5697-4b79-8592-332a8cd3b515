#pragma once
#include <cstdint>
#include <iox/string.hpp>

namespace mower_msgs::srv {

struct ComponentVersion {
  uint16_t major_version;
  uint16_t minor_version;
  uint16_t patch_version;
  uint16_t build_year;
  uint16_t build_month;
  uint16_t build_day;
  uint16_t build_hour;
  uint16_t build_minute;
  uint16_t build_second;
  iox::string<16> git_hash;
};

struct CtqVersionRequest {};

struct CtqVersionResponse {
  bool success;
  ComponentVersion mcu_version;
  ComponentVersion kernel_version;
  ComponentVersion software_version;
  ComponentVersion alg_version;
};
}  // namespace mower_msgs::srv
